import numpy as np
import logging
import time
from typing import Optional, Any

from pyfmto.framework import Client, record_runtime, ClientPackage, ServerPackage
from .iaffbo_utils import (
    AcquisitionFunction,
    generate_pairwise_data_matlab,
    NeuralNetworkClassifier,
    CompetitiveSwarmOptimizer,
    Actions
)
from .addfbo_utils import SmtModel

logger = logging.getLogger('pyfmto')


class IAFFBOClient(Client):
    """
    ucb_flag: 2
    n_samples: 100
    phi: 0.1
    max_iter: 100
    privacy_ratio: 0.0
    flag_transfer: 1
    popsize: 100
    """

    def __init__(self, problem, **kwargs):
        super().__init__(problem)
        kwargs = self.update_kwargs(kwargs)

        # Algorithm parameters exactly matching MATLAB
        self.ucb_flag = kwargs.get('ucb_flag', 2)  # UCB_Flag: 2:LCB 1:UCB 0:EI
        self.n_samples = kwargs.get('n_samples', 100)  # N_notR_ini in MATLAB
        self.phi = kwargs.get('phi', 0.1)  # CSO phi parameter
        self.max_iterations = kwargs.get('max_iter', 100)  # wmax in MATLAB
        self.privacy_ratio = kwargs.get('privacy_ratio', 0.0)  # p in MATLAB
        self.flag_transfer = kwargs.get('flag_transfer', 1)  # Transfer learning flag
        self.popsize = kwargs.get('popsize', 100)  # CSO population size

        # Initialize components
        self.gp_model = None
        self.classifier = None
        self.acq = AcquisitionFunction()
        self.cso = CompetitiveSwarmOptimizer(
            dim=self.dim,
            bounds=(self.x_lb, self.x_ub),
            phi=self.phi,
            max_iterations=self.max_iterations,
            pop_size=self.popsize
        )

        # State variables matching MATLAB
        self.version = 0
        self.theta = 5.0 * np.ones(self.dim)  # THETA{ff} = 5.*ones(1,D);
        self.weights = None
        self.fail_count = 0  # fail_count{ff} in MATLAB
        self.net_agg = None  # Aggregated network weights
        
        # Problem bounds (bu, bd in MATLAB)
        self.bu = self.x_ub  # Upper bounds
        self.bd = self.x_lb  # Lower bounds

    def optimize(self):
        self._optimize_round()

    def is_initialized(self):
        return len(self.solutions.x) > 0

    @record_runtime("Total")
    def _optimize_round(self):
        self._fit_gp()
        self._pull_weights_and_x_hat()  # 移到前面，确保x_hat被初始化
        self._train_classifier()
        self._push_weights()
        new_x = self._find_next_x()
        new_y = self.problem.evaluate(new_x.reshape(1, -1))
        self.solutions.append(new_x.reshape(1, -1), new_y.reshape(1, -1))
        self.version += 1

    @record_runtime("FitGP")
    def _fit_gp(self):
        y_normalized = self._normalize_objectives(self.solutions.y)
        unique_indices = self._get_unique_indices(self.solutions.x)
        x_unique = self.solutions.x[unique_indices]
        y_unique = y_normalized[unique_indices]
        self.gp_model = SmtModel()
        self.gp_model.fit(x_unique, y_unique.ravel())

    @staticmethod
    def _normalize_objectives(y: np.ndarray) -> np.ndarray:
        y_min, y_max = y.min(), y.max()
        if y_max - y_min < 1e-10:
            return np.zeros_like(y)
        return (y - y_min) / (y_max - y_min)

    @staticmethod
    def _get_unique_indices(x: np.ndarray) -> np.ndarray:
        """Get indices of unique rows in x"""
        _, unique_indices = np.unique(x, axis=0, return_index=True)
        return np.sort(unique_indices)

    @record_runtime("TrainClassifier")
    def _train_classifier(self):
        """
        Train classifier exactly matching MATLAB implementation

        MATLAB code structure:
        1. Generate AF values for PopDec samples
        2. Create pairwise data from A1Dec (historical) and PopDec (samples)
        3. Train neural network classifier
        """
        logger.debug(f"Client {self.id} starting classifier training")

        # MATLAB: PopDec = PopDecTest.*(bu-bd)+ones(N_notR_ini,1).*bd;
        pop_dec = self.x_hat * (self.bu - self.bd) + self.bd

        # MATLAB: GP prediction and AF evaluation
        af_samples = self._generate_acquisition_samples(pop_dec)

        # MATLAB: Generate pairwise data
        a1_dec = self.solutions.x  # A1Dec in MATLAB
        a1_obj_norm = self._normalize_objectives(self.solutions.y)  # A1Obj_norm in MATLAB

        pairwise_x, pairwise_y = generate_pairwise_data_matlab(
            a1_dec, a1_obj_norm, pop_dec, af_samples, self.privacy_ratio
        )

        logger.debug(f"Client {self.id} generated {len(pairwise_x)} pairwise samples")

        # MATLAB: xDim = size(TrainIn,2);
        if len(pairwise_x) > 0:
            x_dim = pairwise_x.shape[1]

            # MATLAB: net = patternnet([ceil(xDim*1.5),xDim*1,ceil(xDim/2)]);
            if self.classifier is None:
                hidden_dims = [int(np.ceil(x_dim * 1.5)), int(x_dim * 1), int(np.ceil(x_dim / 2))]
                self.classifier = NeuralNetworkClassifier(x_dim, hidden_dims)
                logger.debug(f"Client {self.id} initialized classifier with input_dim={x_dim}")

            # MATLAB: net = train(net,TrainIn_nor',TrainOut_onehot');
            self.classifier.train(pairwise_x, pairwise_y, self.bd, self.bu, epochs=100)
            self.weights = self.classifier.get_weights()
            logger.debug(f"Client {self.id} trained classifier and extracted weights")
        else:
            # Handle empty pairwise data case
            logger.warning(f"Client {self.id} has no pairwise data, using default classifier")
            if self.classifier is None:
                x_dim = self.dim * 2  # Default dimension
                hidden_dims = [int(np.ceil(x_dim * 1.5)), x_dim, int(np.ceil(x_dim / 2))]
                self.classifier = NeuralNetworkClassifier(x_dim, hidden_dims)
                logger.debug(f"Client {self.id} initialized default classifier with input_dim={x_dim}")
            self.weights = self.classifier.get_weights()
            logger.debug(f"Client {self.id} extracted default weights")

    def _generate_acquisition_samples(self, pop_dec: np.ndarray) -> np.ndarray:
        """
        Generate acquisition function samples exactly matching MATLAB
        
        MATLAB code:
        for i = 1: N_notR_ini
            [PopObj(i,:),~,MSE(i,:)] = predictor(PopDec(i,:),Model{ff});
        end
        objs = AF(PopObj,MSE,A1Obj_norm,UCB_Flag);
        """
        n_samples = len(pop_dec)
        pop_obj = np.zeros(n_samples)
        mse = np.zeros(n_samples)
        
        # MATLAB: [PopObj(i,:),~,MSE(i,:)] = predictor(PopDec(i,:),Model{ff});
        # Batch processing for speed while maintaining MATLAB logic
        mean, std = self.gp_model.predict(pop_dec, return_std=True)
        pop_obj = mean.ravel()
        mse = (std.ravel()) ** 2  # Convert std to MSE (variance)
        
        # MATLAB: objs = AF(PopObj,MSE,A1Obj_norm,UCB_Flag);
        a1_obj_norm = self._normalize_objectives(self.solutions.y)
        af_values = self.acq.eval(pop_obj, mse, a1_obj_norm, self.ucb_flag)
        
        return af_values

    @record_runtime("Push")
    def _push_weights(self):
        if self.weights is None:
            logger.warning(f"Client {self.id} cannot push weights: weights is None")
            return

        data = {'version': self.version, 'weights': self.weights}
        pkg = ClientPackage(self.id, Actions.PUSH_WEIGHTS, data)
        response = self._request_server_with_detailed_timing(pkg, repeat=3, operation="PUSH_WEIGHTS")
        # _request_server_with_detailed_timing already logs SUCCESS/FAILED, so we don't need additional logging here

    @record_runtime("Pull")
    def _pull_weights_and_x_hat(self):
        """
        Pull aggregated weights and x_hat from server

        This corresponds to the MATLAB transfer learning mechanism where
        clients receive aggregated weights from similar tasks
        """
        # First get x_hat if not available
        if not hasattr(self, 'x_hat') or self.x_hat is None:
            self._pull_x_hat_init()

        # Then try to get aggregated weights
        self._pull_aggregated_weights()

    @record_runtime("Pull_X_Hat_Init")
    def _pull_x_hat_init(self):
        """Pull initial x_hat from server"""
        pkg = ClientPackage(self.id, Actions.PULL_INIT, {'dim': self.dim})
        response = self.request_server(pkg)
        if response is not None and response.data is not None:
            self.x_hat = response.data
        else:
            # Fallback: generate x_hat locally if server response fails
            from pyDOE import lhs
            self.x_hat = lhs(self.dim, 100)  # Default n_samples=100

    @record_runtime("Pull_Aggregated_Weights")
    def _pull_aggregated_weights(self):
        """Pull aggregated weights from server with retry mechanism"""
        pkg = ClientPackage(self.id, Actions.PULL_WEIGHTS, {'version': self.version})
        # 使用更智能的重试策略：先快速尝试，如果失败则适度等待
        response = self._request_server_with_detailed_timing(pkg, repeat=1, operation="PULL_WEIGHTS")

        if response is not None and response.data is not None:
            # Store aggregated weights for potential use in CSO
            self.net_agg = response.data['weights']
            if 'x_hat' in response.data:
                self.x_hat = response.data['x_hat']
        else:
            # No aggregated weights available yet
            self.net_agg = None
        # _request_server_with_detailed_timing already logs SUCCESS/FAILED

    def _request_server_with_detailed_timing(self, package: Any, repeat: int = 10,
                                           interval: float = 1.0, operation: str = "REQUEST") -> Optional[ServerPackage]:
        """
        Enhanced request_server with detailed timing information
        """
        start_time = time.time()
        attempt_times = []

        logger.info(f"Client {self.id} starting {operation} with repeat={repeat}")

        repeat_max = max(1, repeat)
        curr_repeat = 1

        while curr_repeat <= repeat_max:
            attempt_start = time.time()

            try:
                response = self.request_server(package, repeat=1, interval=0)  # Single attempt
                attempt_end = time.time()
                attempt_duration = attempt_end - attempt_start
                attempt_times.append(attempt_duration)

                if self.check_pkg(response):
                    total_time = time.time() - start_time
                    logger.info(f"Client {self.id} {operation} SUCCESS after {curr_repeat} attempts, "
                              f"total_time={total_time:.2f}s, avg_attempt={np.mean(attempt_times):.3f}s")
                    return response
                else:
                    logger.debug(f"Client {self.id} {operation} attempt {curr_repeat} failed check_pkg, "
                               f"attempt_time={attempt_duration:.3f}s")

            except Exception as e:
                attempt_end = time.time()
                attempt_duration = attempt_end - attempt_start
                attempt_times.append(attempt_duration)
                logger.warning(f"Client {self.id} {operation} attempt {curr_repeat} exception: {e}, "
                             f"attempt_time={attempt_duration:.3f}s")

            curr_repeat += 1
            if curr_repeat <= repeat_max:
                time.sleep(interval)

        total_time = time.time() - start_time
        logger.warning(f"Client {self.id} {operation} FAILED after {repeat_max} attempts, "
                      f"total_time={total_time:.2f}s, avg_attempt={np.mean(attempt_times):.3f}s")
        return None

    @record_runtime("CSO")
    def _find_next_x(self) -> np.ndarray:
        """
        Find next solution using CSO exactly matching MATLAB implementation
        
        MATLAB code includes:
        1. Failure count mechanism
        2. Transfer learning decision
        3. CSO optimization with existing solutions
        """
        # MATLAB: Failure count mechanism
        if len(self.solutions.y) >= 2:
            current_best = np.min(self.solutions.y)
            previous_best = np.min(self.solutions.y[:-1])
            if current_best == previous_best:
                self.fail_count += 1
            else:
                self.fail_count = 0
        else:
            self.fail_count = 0
        
        # MATLAB: Transfer learning decision
        # if flag_transfer == 1 && rand < 0.5
        net_opt = self.classifier
        if self.flag_transfer == 1 and self.net_agg is not None:
            if np.random.rand() < 0.5:
                # Use aggregated weights
                net_opt = NeuralNetworkClassifier(
                    self.classifier.input_dim, 
                    self.classifier.hidden_dims
                )
                net_opt.set_weights(self.net_agg)
        
        # MATLAB: Prepare existing solutions (Decs_temp)
        existing_solutions = None
        if len(self.solutions.x) > 0:
            # MATLAB: Decs_temp = [PopNewDec{ff}];
            existing_solutions = self.solutions.x[-1:] if len(self.solutions.x) > 0 else None
        
        # MATLAB: CSO optimization
        best_x = self.cso.optimize(net_opt, self.bd, self.bu, existing_solutions)
        
        # Ensure solution is not duplicate (simple check)
        max_attempts = 10
        attempts = 0
        while attempts < max_attempts and len(self.solutions.x) > 0:
            # Check if solution is too close to existing ones
            distances = np.linalg.norm(self.solutions.x - best_x, axis=1)
            if np.min(distances) > 1e-6:  # Not too close
                break
            # Generate new solution
            best_x = self.cso.optimize(net_opt, self.bd, self.bu, None)
            attempts += 1
        
        return best_x

    def check_pkg(self, x: ServerPackage) -> bool:
        if x is None:
            return False
        return x.data is not None

    @property
    def x(self) -> np.ndarray:
        return self.solutions.x

    @property
    def y(self) -> np.ndarray:
        return self.solutions.y